#!/bin/bash

# Token Library Deployment Script
echo "🚀 Starting Token Library deployment..."

# Check if we're in the right directory
if [ ! -d "token-library" ]; then
    echo "❌ Error: token-library directory not found!"
    echo "Please run this script from /opt/tools/token-library/"
    exit 1
fi

# Navigate to the app directory
cd token-library

echo "📦 Installing dependencies..."
npm install

echo "🔨 Building the application..."
npm run build

# Check if build was successful
if [ $? -ne 0 ]; then
    echo "❌ Build failed! Please check the errors above."
    exit 1
fi

echo "📋 Setting up systemd service..."

# Copy service file to systemd directory
sudo cp ../token-library.service /etc/systemd/system/

# Reload systemd daemon
sudo systemctl daemon-reload

# Enable the service (start on boot)
sudo systemctl enable token-library

# Start the service
sudo systemctl start token-library

# Check service status
echo "📊 Service status:"
sudo systemctl status token-library --no-pager

echo ""
echo "✅ Deployment completed!"
echo ""
echo "📋 Useful commands:"
echo "  Check status:    sudo systemctl status token-library"
echo "  View logs:       sudo journalctl -u token-library -f"
echo "  Restart:         sudo systemctl restart token-library"
echo "  Stop:            sudo systemctl stop token-library"
echo "  Disable:         sudo systemctl disable token-library"
echo ""
echo "🌐 Application should be running on: http://localhost:9032"
