[Unit]
Description=Token Library Next.js Application
Documentation=https://github.com/your-repo/token-library
After=network.target
Wants=network.target

[Service]
Type=simple
User=johnson
Group=johnson
WorkingDirectory=/opt/tools/token-library/token-library
Environment=NODE_ENV=production
Environment=PORT=9032
ExecStart=/usr/bin/npm start
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
StandardOutput=syslog
StandardError=syslog
SyslogIdentifier=token-library

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/tools/token-library/token-library/data

[Install]
WantedBy=multi-user.target
